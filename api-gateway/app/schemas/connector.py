from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


# Enums from connector.proto
class SourceType(str, Enum):
    GOOGLE_DRIVE = "GOOGLE_DRIVE"
    SLACK = "SLACK"
    JIRA = "JIRA"


class ConnectorType(str, Enum):
    STRUCTURED = "STRUCTURED"
    UNSTRUCTURED = "UNSTRUCTURED"


# Source model
class SourceModel(BaseModel):
    id: str
    organisation_id: str
    type: SourceType
    name: str
    created_at: str
    updated_at: str
    connector_type: ConnectorType
    status: str = "active"  # "active", "inactive", "error"
    last_sync_at: str = ""
    metadata: Dict[str, str] = {}


# File info model for responses
class FileInfo(BaseModel):
    id: str
    name: str
    mime_type: str = ""
    size: int = 0
    created_at: str = ""
    modified_at: str = ""


# Folder model for responses
class Folder(BaseModel):
    id: str
    name: str
    parent_id: str = ""
    child_count: int = 0
    created_at: str = ""
    modified_at: str = ""


# Add Source models
class AddSourceRequest(BaseModel):
    organisation_id: str
    type: SourceType
    name: str
    key: str  # Service Account JSON or API key (required)
    file_ids: Optional[List[str]] = (
        None  # Optional list of specific file IDs to sync (for Google Drive)
    )
    jira_url: Optional[str] = None  # Optional: Jira URL (for Jira sources)
    jira_email: Optional[str] = None  # Optional: Jira email (for Jira sources)
    config: Optional[Dict[str, str]] = None  # Additional connector-specific configuration


class AddSourceResponse(BaseModel):
    success: bool
    message: str
    source: Optional[SourceModel] = None
    synced_files: List[FileInfo] = []  # List of files that were synced


# List Sources models
class ListSourcesRequest(BaseModel):
    organisation_id: str
    type: Optional[SourceType] = None  # Filter by source type
    connector_type: Optional[ConnectorType] = None  # Filter by connector type


class ListSourcesResponse(BaseModel):
    success: bool
    message: str
    sources: List[SourceModel] = []
    isInitialMapping: bool = (
        False  # True if at least one department other than general has access to at least one folder
    )


# Delete Source models
class DeleteSourceRequest(BaseModel):
    source_id: str
    user_id: str  # Admin user ID
    organisation_id: str


class DeleteSourceResponse(BaseModel):
    success: bool
    message: str


# Update Source Credentials models
class UpdateSourceCredentialsRequest(BaseModel):
    source_id: str
    user_id: str
    key: str  # Service Account JSON or API key (required)
    jira_url: Optional[str] = None  # Optional: Jira URL (for Jira sources)
    jira_email: Optional[str] = None  # Optional: Jira email (for Jira sources)
    config: Optional[Dict[str, str]] = None  # Additional connector-specific configuration


class UpdateSourceCredentialsResponse(BaseModel):
    success: bool
    message: str
    source: Optional[SourceModel] = None


# Validate Source models
class ValidateSourceRequest(BaseModel):
    source_id: str
    organisation_id: str


class ValidateSourceResponse(BaseModel):
    success: bool
    message: str
    accessible_folders: List[Folder] = []
    connection_status: str = ""  # "connected", "disconnected", "error"


# Connector capability information
class ConnectorCapability(BaseModel):
    name: str
    description: str
    supported: bool


# Connector information model
class ConnectorInfo(BaseModel):
    type: SourceType
    connector_type: ConnectorType
    name: str
    description: str
    version: str
    capabilities: List[ConnectorCapability] = []
    supported_file_types: List[str] = []
    configuration_schema: Dict[str, str] = {}


# Get Connector Info models
class GetConnectorInfoRequest(BaseModel):
    type: SourceType


class GetConnectorInfoResponse(BaseModel):
    success: bool
    message: str
    connector_info: Optional[ConnectorInfo] = None


# List Connector Types models
class ListConnectorTypesRequest(BaseModel):
    connector_type: Optional[ConnectorType] = None  # Filter by connector type


class ListConnectorTypesResponse(BaseModel):
    success: bool
    message: str
    available_connectors: List[ConnectorInfo] = []


# Test Connection models
class TestConnectionRequest(BaseModel):
    source_id: str
    organisation_id: str


class TestConnectionResponse(BaseModel):
    success: bool
    message: str
    connection_status: str = ""  # "connected", "disconnected", "error"
    connection_details: Dict[str, str] = {}  # Additional connection information


# Sync Source models
class SyncSourceRequest(BaseModel):
    user_id: str
    source_id: str
    organisation_id: str
    full_sync: bool = False  # Whether to perform full sync or incremental
    specific_items: Optional[List[str]] = None  # Optional: specific files/folders to sync


class SyncSourceResponse(BaseModel):
    success: bool
    message: str
    sync_id: str = ""  # Unique identifier for this sync operation
    sync_status: str = ""  # "started", "in_progress", "completed", "failed"
    items_queued: int = 0  # Number of items queued for sync


# Get Sync Status models
class GetSyncStatusRequest(BaseModel):
    source_id: str
    organisation_id: str
    sync_id: Optional[str] = None  # Optional: specific sync operation ID


# Sync statistics model
class SyncStats(BaseModel):
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    started_at: str = ""
    completed_at: str = ""
    estimated_completion: str = ""


class GetSyncStatusResponse(BaseModel):
    success: bool
    message: str
    sync_status: str = ""  # "idle", "in_progress", "completed", "failed"
    sync_stats: Optional[SyncStats] = None
    error_messages: List[str] = []  # Any error messages from sync
    last_sync_at: str = ""


# Google Drive specific models


# Simple folder info model
class FolderInfo(BaseModel):
    id: str
    name: str


# Disconnect Drive models
class DisconnectDriveRequest(BaseModel):
    organisation_id: str


class DisconnectDriveResponse(BaseModel):
    success: bool
    message: str


# List Files models
class ListFilesRequest(BaseModel):
    user_id: str
    folder_id: Optional[str] = None  # Optional, to list files in a specific folder
    page: int = 1
    page_size: int = 10
    organisation_id: str  # Organisation ID for source context


# Google Drive file model
class DriveFileModel(BaseModel):
    id: str
    name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    parent_folder_id: str
    size: int
    shared_with: List[str] = []  # List of user emails with access
    is_folder: bool
    child_count: int = 0  # Number of children (for folders)


class ListFilesResponse(BaseModel):
    success: bool
    message: str
    files: List[DriveFileModel] = []
    total_count: int
    page: int
    page_size: int


# Get File Details models
class GetFileDetailsRequest(BaseModel):
    user_id: str
    file_id: str
    organisation_id: str


class GetFileDetailsResponse(BaseModel):
    success: bool
    message: str
    file: Optional[DriveFileModel] = None


# Get Folder By ID models
class GetFolderByIdRequest(BaseModel):
    organisation_id: str  # Organisation ID for service account
    folder_id: str


class GetFolderByIdResponse(BaseModel):
    success: bool
    message: str
    folder: Optional[DriveFileModel] = None
    children: List[DriveFileModel] = []


# Sync Folder By IDs models
class SyncFolderByIdsRequest(BaseModel):
    organisation_id: str  # Organisation ID for service account
    folder_ids: List[str]  # List of folder IDs to sync


class SyncFolderByIdsResponse(BaseModel):
    success: bool
    message: str
    files_synced: int
    folders_synced: int
    sync_status: str
    synced_folders: List[FolderInfo] = []  # List of synced folders


# Check File Access models
class CheckFileAccessRequest(BaseModel):
    user_id: str
    file_id: str
    organisation_id: str


class CheckFileAccessResponse(BaseModel):
    success: bool
    message: str
    has_access: bool


# Sync File By URL models
class SyncFileByUrlRequest(BaseModel):
    drive_url: List[str]  # Google Drive URLs
    agent_id: str  # Agent ID
    user_id: Optional[str] = None  # Optional user ID
    organisation_id: str  # Organisation ID


# Information about a synced file
class SyncedFileInfo(BaseModel):
    file_id: str  # The ID of the synced file
    file_name: str  # The name of the synced file
    drive_url: str  # The original URL that was synced
    sync_status: str  # "completed" or "failed"
    error_message: str = ""  # Error message if sync failed


class SyncFileByUrlResponse(BaseModel):
    success: bool
    message: str
    synced_files: List[SyncedFileInfo] = []  # Information about all synced files
    total_files: int  # Total number of URLs processed
    successful_syncs: int  # Number of successful syncs
    failed_syncs: int  # Number of failed syncs


# List Top Level Folders models
class ListTopLevelFoldersRequest(BaseModel):
    organisation_id: str  # Organisation ID for service account


class ListTopLevelFoldersResponse(BaseModel):
    success: bool
    message: str
    folders: List[FolderInfo] = []  # List of top-level folders
